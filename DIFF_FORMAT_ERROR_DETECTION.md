# Diff 格式错误检测改进

## 问题描述

之前的 ToolExecutor 在处理 `replace_in_file` 工具调用时，对于错误的 diff 格式无法正确识别并报错。特别是当模型生成如下错误格式时：

```xml
<replace_in_file>
<path>implementation_plan.md</path>
<diff>
------- SEARCH
## Functions
...
------- REPLACE
## Functions
...
</diff>
</replace_in_file>
```

这种格式会被当做普通的 replace 命令来尝试修改，但实际上不会修改任何内容，也不会给出明确的错误提示。

## 解决方案

### 1. 添加错误格式检测

在 `src/core/assistant-message/diff.ts` 中添加了以下改进：

#### 新增的正则表达式模式
```typescript
// 检测常见错误 diff 格式的正则表达式
const INCORRECT_XML_TAG_REGEX = /^<(replace_in_file|path|diff)>/i
const INCORRECT_DIFF_MARKER_REGEX = /^[-]{3,} (SEARCH|REPLACE)$/i
```

#### 单行错误检测函数
```typescript
function detectIncorrectDiffFormat(line: string): string | null
```
用于检测单行中的错误格式，如错误的 diff 标记。

#### 整体内容错误检测函数
```typescript
function detectIncorrectDiffFormatInContent(diffContent: string): string | null
```
用于检测整个 diff 内容中的 XML 标签等错误格式。

### 2. 在处理流程中集成检测

#### 在 `constructNewFileContent` 函数开头添加检查
```typescript
export async function constructNewFileContent(
    diffContent: string,
    originalContent: string,
    isFinal: boolean,
    version: "v1" | "v2" = "v1",
): Promise<string> {
    // 首先检查整个内容中的错误格式
    const incorrectFormatError = detectIncorrectDiffFormatInContent(diffContent)
    if (incorrectFormatError) {
        throw new Error(`Incorrect diff format detected: ${incorrectFormatError}`)
    }
    // ... 其余处理逻辑
}
```

#### 在 `internalProcessLine` 方法中添加逐行检查
```typescript
private internalProcessLine(line: string, ...): number {
    // 检查错误的 diff 格式
    const incorrectFormatError = detectIncorrectDiffFormat(line)
    if (incorrectFormatError) {
        throw new Error(`Incorrect diff format detected: ${incorrectFormatError}`)
    }
    // ... 其余处理逻辑
}
```

## 检测的错误格式类型

### 1. XML 标签格式
- `<replace_in_file>`
- `<path>`
- `<diff>`
- `</replace_in_file>`
- `</path>`
- `</diff>`

### 2. 错误的 diff 标记
- `------- REPLACE` (应该是 `------- SEARCH`)
- 其他不符合标准格式的标记

## 错误提示信息

当检测到错误格式时，会抛出详细的错误信息，指导用户使用正确的格式：

```
XML-style tool usage detected. This appears to be an incorrect format. 
Please use the standard SEARCH/REPLACE block format directly:

------- SEARCH
[content to find]
=======
[content to replace with]
+++++++ REPLACE

Do not wrap the diff content in XML tags like <replace_in_file>, <path>, or <diff>.
```

## 测试验证

改进后的代码能够正确检测以下错误格式：

1. ✅ XML 标签包装的 diff 内容
2. ✅ 错误的 diff 标记格式
3. ✅ 混合错误格式
4. ✅ 正确格式仍然正常工作

## 兼容性

- 不影响现有的正确 diff 格式处理
- 向后兼容所有现有功能
- 只在检测到明确的错误格式时才抛出错误

## 总结

这个改进解决了用户提到的问题，现在 ToolExecutor 能够：

1. **准确检测**错误的 diff 格式
2. **明确报错**并提供有用的错误信息
3. **指导用户**使用正确的格式
4. **保持兼容性**不影响现有功能

用户不再会遇到错误格式被静默忽略的问题，而是会收到清晰的错误提示，帮助他们纠正格式问题。
