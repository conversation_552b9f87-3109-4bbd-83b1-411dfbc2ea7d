# Diff 格式错误检测改进

## 问题描述

之前的 ToolExecutor 在处理 `replace_in_file` 工具调用时，对于错误的 diff 格式无法正确识别并报错。

## 正确的格式说明

首先需要澄清正确的 `replace_in_file` 工具格式：

```xml
<replace_in_file>
<path>File path here</path>
<diff>
------- SEARCH
  [exact content to find]
  =======
  [new content to replace with]
  +++++++ REPLACE
</diff>
<task_progress>
[] optional task
</task_progress>
</replace_in_file>
```

其中 `<task_progress>` 为可选标签。

## 可能出现的错误情况

1. **消息解析失败**：XML 标签没有被正确解析，导致包含 XML 标签的内容被直接传递给 diff 处理器
2. **错误的 diff 标记**：使用了错误的标记格式，如 `------- REPLACE` 而不是 `------- SEARCH`
3. **空的 diff 内容**：diff 参数为空或只包含空白字符
4. **其他 XML 内容混入**：diff 内容中意外包含了其他 XML 标签

## 解决方案

### 1. 添加错误格式检测

在 `src/core/assistant-message/diff.ts` 中添加了以下改进：

#### 新增的正则表达式模式
```typescript
// 检测错误的 diff 标记格式
const INCORRECT_DIFF_MARKER_REGEX = /^[-]{3,} (REPLACE)$/i  // Should be SEARCH, not REPLACE
```

#### 单行错误检测函数
```typescript
function detectIncorrectDiffFormat(line: string): string | null
```
用于检测单行中的错误格式，如错误的 diff 标记。

#### 整体内容错误检测函数
```typescript
function detectIncorrectDiffFormatInContent(diffContent: string): string | null
```
用于检测整个 diff 内容中的问题，包括：
- 空的 diff 内容
- 意外包含的 XML 标签（表明消息解析失败）
- 其他不应该出现在纯 diff 内容中的 XML 标记

### 2. 在处理流程中集成检测

#### 在 `constructNewFileContent` 函数开头添加检查
```typescript
export async function constructNewFileContent(
    diffContent: string,
    originalContent: string,
    isFinal: boolean,
    version: "v1" | "v2" = "v1",
): Promise<string> {
    // 首先检查整个内容中的错误格式
    const incorrectFormatError = detectIncorrectDiffFormatInContent(diffContent)
    if (incorrectFormatError) {
        throw new Error(`Incorrect diff format detected: ${incorrectFormatError}`)
    }
    // ... 其余处理逻辑
}
```

#### 在 `internalProcessLine` 方法中添加逐行检查
```typescript
private internalProcessLine(line: string, ...): number {
    // 检查错误的 diff 格式
    const incorrectFormatError = detectIncorrectDiffFormat(line)
    if (incorrectFormatError) {
        throw new Error(`Incorrect diff format detected: ${incorrectFormatError}`)
    }
    // ... 其余处理逻辑
}
```

## 检测的错误格式类型

### 1. 空的 diff 内容
- 完全空白的 diff 参数
- 只包含空白字符的 diff 内容

### 2. 意外的 XML 标签
- `<replace_in_file>`, `<path>`, `<diff>` 等标签出现在 diff 内容中
- 表明消息解析失败，XML 标签没有被正确处理

### 3. 错误的 diff 标记
- `------- REPLACE` (应该是 `------- SEARCH`)
- 其他不符合标准格式的标记

### 4. 其他 XML 内容
- diff 内容中包含任何其他 XML 标签

## 错误提示信息

当检测到错误格式时，会抛出详细的错误信息，指导用户使用正确的格式：

### 空内容错误
```
Empty diff content detected. Diff content must contain SEARCH/REPLACE blocks:

------- SEARCH
[exact content to find]
=======
[new content to replace with]
+++++++ REPLACE
```

### XML 标签错误
```
XML tags detected in diff content. This suggests the message parsing failed.
The diff content should contain only SEARCH/REPLACE blocks:

------- SEARCH
[exact content to find]
=======
[new content to replace with]
+++++++ REPLACE

XML tags like <replace_in_file>, <path>, <diff> should be handled by the message parser,
not passed directly to the diff processor.
```

### 错误标记格式
```
Incorrect diff marker format detected. The correct format should be:
'------- SEARCH' (not '------- REPLACE')
'=======' (separator)
'+++++++ REPLACE'
```

## 测试验证

改进后的代码能够正确检测以下错误格式：

1. ✅ 空的 diff 内容
2. ✅ 意外包含 XML 标签的 diff 内容（消息解析失败）
3. ✅ 错误的 diff 标记格式
4. ✅ 包含其他 XML 标签的 diff 内容
5. ✅ 正确的纯 SEARCH/REPLACE 格式仍然正常工作

## 兼容性

- 不影响现有的正确 diff 格式处理
- 向后兼容所有现有功能
- 只在检测到明确的错误格式时才抛出错误

## 重要说明

这个改进基于对正确格式的重新理解：

- **正确的完整格式**是包含 XML 标签的结构化格式
- **消息解析器**负责解析 XML 标签并提取 diff 内容
- **diff 处理器**只处理纯粹的 SEARCH/REPLACE 块内容
- **错误检测**主要针对消息解析失败或格式错误的情况

## 总结

这个改进现在能够正确处理 `replace_in_file` 工具的错误格式检测：

1. **理解正确格式**：XML 标签结构是正确的，不应被当作错误
2. **检测真正的错误**：空内容、解析失败、错误标记等
3. **提供有用的错误信息**：明确指出问题所在和解决方法
4. **保持功能完整性**：不影响正确格式的处理

现在当出现真正的格式错误时，用户会收到准确的错误提示，帮助他们理解和纠正问题。
